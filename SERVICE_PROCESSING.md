## CRM Service & Task Architecture Design

### 1. Схема базы данных

#### 1.1 Core Entities

| Entity                     | Описание                                            | Поля                                                                                                                                                                                                               | Связи                                                         |
| -------------------------- | --------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------- |
| **Client**                 | Клиент (компания)                                   | `id PK`, `name`, `ein`, `active_since`, `status`                                                                                                                                                                   | 1─\* ClientService; 1─\* TaxReport; 1─\* Task                 |
| **Service**                | Определение услуги или пакета                       | `id PK`, `code`, `name`, `description`, `price_type {one-off, monthly, yearly}`, `composite_flag`                                                                                                                  | 1─\* ServiceComponent                                         |
| **ServiceComponent**       | Шаблон компонента составной услуги                  | `id PK`, `parent_service_id FK<Service>`, `component_service_id FK<Service>`, `sequence`                                                                                                                           | \*─1 Service (parent); \*─1 Service (child)                   |
| **ClientService**          | Применение услуги к клиенту, в том числе составной  | `id PK`, `client_id FK<Client>`, `service_id FK<Service>`, `start_date`, `end_date?`, `discount_percent?`, `discount_amount?`, `total?`                                                                            | *─1 Client; *─1 Service; 1─* Task; 1─* ClientServiceComponent |
| **ClientServiceComponent** | Компоненты применённого клиентом составного сервиса | `id PK`, `client_service_id FK<ClientService>`, `service_id FK<Service>` (компонент), `sequence`, `custom_price?`                                                                                                  | \*─1 ClientService; \*─1 Service (компонент)                  |
| **Task**                   | Рабочая задача                                      | `id PK`, `client_service_id FK<ClientService>?`, `tax_report_id FK<TaxReport>?`, `title`, `description`, `due_date`, `assignee_manager_id`, `status {OPEN, IN_PROGRESS, DONE}`                                     | \*─1 ClientService or \*─1 TaxReport                          |
| **TaxReport**              | Налоговая отчётность                                | `id PK`, `client_id FK<Client>`, `name`, `fiscal_year`, `assignee_manager_id`, `status {DOCUMENT_COLLECTION, PREPARATION, REVIEW, AWAITING_SIGNATURE, FILED, DONE}`, `progress_steps JSON`, `file_id`, `file_name` | *─1 Client; 1─* Task                                          |
| **AlertRule**              | Правило оповещения                                  | `id PK`, `model_type {Task, TaxReport}`, `offset_days`, `frequency {once, daily}`, `repeat_until_due`, `channel {email, slack, in_app}`                                                                            | 1─\* Notification                                             |
| **Notification**           | Сформированное оповещение                           | `id PK`, `alert_rule_id FK<AlertRule>`, `target_user_id`, `entity_id`, `sent_at`, `status {SENT, FAILED}`                                                                                                          | \*─1 AlertRule                                                |

### 2. Механизм взаимодействия и воркфлоу

1. **Применение услуги**:

   * При создании записи в `ClientService`:

     * Если услуга **не составная**, сразу создаются связанные `ClientServiceComponent` не нужны, и создаётся одна `Task`.
     * Если услуга **составная** (`composite_flag = true`):

       1. Из шаблонов `ServiceComponent` по `service_id` формируется ordered list компонентов.
       2. Создаётся для каждого компонента запись в `ClientServiceComponent` с полем `sequence`, копируящим порядок, и опциональным `custom_price`.
       3. На основе `ClientServiceComponent` генерируются `Task` по каждому компоненту (или пакетно, в зависимости от SLA).
     * Клиенту доступен UI для **добавления**, **удаления** и **перестановки** компонентов в `ClientServiceComponent`, не затрагивая оригинальный шаблон `ServiceComponent`.

2. **Управление задачами**:

   * `Task` имеет статус и дедлайн. Менеджер включает в себя логику изменения статуса в UI.
   * Новые задачи не генерируются автоматически планировщиком: генерация происходит только при добавлении услуги или вручную.

3. **Tax Reporting**:

   * При создании `TaxReport` устанавливается статус `DOCUMENT_COLLECTION` и создаётся чек-лист в `progress_steps`.
   * Шаги прогресса (сбор данных, подготовка, проверка, подписание, подача) отражаются в `status` и `progress_steps`.
   * После загрузки файла (`file_id`, `file_name`) статус меняется на `DONE` и связанные задачи закрываются.

4. **Оповещения и алерты**:

   * `AlertRule` определяет, за сколько дней до дедлайна и с какой частотой отправлять уведомления.
   * **Scheduler (Taskiq + Redis)**: запускается ежечасно, выбирает все активные `AlertRule` и проверяет задачи (`Task`) и отчёты (`TaxReport`) на условия оповещений.
   * При совпадении создаётся запись `Notification` и отправляется через указанный канал.
   * Если `repeat_until_due = true`, уведомления повторяются каждый период до дедлайна.

### 3. План реализации

#### Фаза 1: Проектирование и настройка

* Создать SQLAlchemy-модели для всех сущностей и связей.
* Настроить миграции (Alembic) для схемы БД.
* Разработать репозитории/DAO слои для CRUD операций.

#### Фаза 2: Бизнес-логика и workflow

* Реализовать сервис-слой на FastAPI:

  * Методы для создания `ClientService`, `Task`, `TaxReport`, `AlertRule`.
  * Обработчики для расширения составных услуг и создания задач.
* Внедрить логику изменения статусов и управления прогрессом налоговых отчётов.

#### Фаза 3: Система уведомлений

* Интегрировать Taskiq + Redis:

  * Написать планировщик, который вовремя запускает отправку уведомлений.
  * Имплементировать механизмы проверки `AlertRule` и создания `Notification`.
* Подключить адаптеры отправки (SMTP для email, Slack API, in-app сокеты).

#### Фаза 4: Хранение файлов

* Реализовать хранилище:

  * Сделать БД таблицу `files` для метаданных.
  * Генерация `file_id` и сохранение файла через RemoteManager.
  * Сервис для отдачи файлов по `file_id`.
